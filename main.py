import os
import sys
import psutil
import subprocess
import threading
import time
import tkinter as tk
from tkinter import ttk
import webbrowser
from PIL import Image, ImageTk, ImageSequence
import urllib.request
import zipfile
from tkinter import messagebox
from tkinter.ttk import Progressbar
import re
import requests

from packaging import version
import math

# Determine base directory
if getattr(sys, 'frozen', False):
    script_dir = os.path.dirname(sys.executable)
else:
    script_dir = os.path.dirname(os.path.abspath(__file__))

class SplashScreen:
    def __init__(self):
        self.splash = tk.Toplevel()
        self.splash.overrideredirect(True)
        self.splash.attributes('-alpha', 0.0)
        
        # Get screen dimensions
        screen_width = self.splash.winfo_screenwidth()
        screen_height = self.splash.winfo_screenheight()
        
        # Set splash window size
        splash_width = 400
        splash_height = 300
        
        # Calculate position for center of screen
        x = (screen_width - splash_width) // 2
        y = (screen_height - splash_height) // 2
        
        self.splash.geometry(f'{splash_width}x{splash_height}+{x}+{y}')
        
        # Create main frame
        self.frame = tk.Frame(self.splash, bg='#1f2126')
        self.frame.pack(fill='both', expand=True)
        
        # Load and display logo
        try:
            logo_path = os.path.join(script_dir, "logo.png")
            if not os.path.exists(logo_path):
                # Download logo if not exists
                try:
                    logo_url = "https://aion-blitz.online/aion-blitz/Game-Launcher/Images/logo.png"
                    urllib.request.urlretrieve(logo_url, logo_path)
                except Exception as download_error:
                    print(f"Could not download logo: {download_error}")
                    # Create a placeholder logo file or skip logo loading
                    return

            logo_img = Image.open(logo_path)
            logo_img = logo_img.resize((200, 200), Image.Resampling.LANCZOS)
            self.logo_photo = ImageTk.PhotoImage(logo_img)
            self.logo_label = tk.Label(self.frame, image=self.logo_photo, bg='#1f2126')
            self.logo_label.pack(pady=20)
        except Exception as e:
            print(f"Error loading logo: {e}")
        
        # Loading text
        self.loading_label = tk.Label(self.frame, text="Loading...", font=("Arial", 12), bg='#1f2126', fg='white')
        self.loading_label.pack(pady=10)
        
        # Progress bar
        self.progress = ttk.Progressbar(self.frame, length=300, mode='determinate', style="Splash.Horizontal.TProgressbar")
        self.progress.pack(pady=10)
        
        # Configure progress bar style
        style = ttk.Style()
        style.configure("Splash.Horizontal.TProgressbar", 
                       troughcolor='#1f2126',
                       background='#ff8800',
                       thickness=10)
        
        # Start fade in animation
        self.fade_in()
        
    def fade_in(self):
        alpha = self.splash.attributes('-alpha')
        if alpha < 1.0:
            alpha += 0.1
            self.splash.attributes('-alpha', alpha)
            self.splash.after(20, self.fade_in)
        else:
            self.animate_progress()
    
    def animate_progress(self):
        value = self.progress['value']
        if value < 100:
            value += 2
            self.progress['value'] = value
            self.splash.after(20, self.animate_progress)
        else:
            self.fade_out()
    
    def fade_out(self):
        alpha = self.splash.attributes('-alpha')
        if alpha > 0:
            alpha -= 0.1
            self.splash.attributes('-alpha', alpha)
            self.splash.after(20, self.fade_out)
        else:
            self.splash.destroy()

class ModernLauncher:
    def __init__(self):
        self.window = tk.Tk()
        self.window.title("Aion-Blitz Launcher")
        self.window.configure(bg="#0a0a0a")
        self.window.resizable(False, False)

        # Remove window decorations for modern look
        self.window.overrideredirect(True)

        # Show splash screen
        self.splash = SplashScreen()

        # Configure window size and position
        window_width, window_height = 900, 600
        screen_width, screen_height = self.window.winfo_screenwidth(), self.window.winfo_screenheight()
        x, y = (screen_width // 2) - (window_width // 2), (screen_height // 2) - (window_height // 2)
        self.window.geometry(f"{window_width}x{window_height}+{x}+{y}")

        # Add custom title bar
        self.create_title_bar()

        # Create main content area
        self.create_main_content()

        # Configure styles
        self.configure_styles()

        # Start update check after splash screen
        self.window.after(500, self.check_updates)

        # Make window draggable
        self.make_draggable()
        
    def create_title_bar(self):
        # Custom title bar
        self.title_bar = tk.Frame(self.window, bg="#1a1a1a", height=40)
        self.title_bar.pack(fill="x", side="top")
        self.title_bar.pack_propagate(False)

        # Title
        title_label = tk.Label(self.title_bar, text="Aion-Blitz Launcher",
                              font=("Segoe UI", 12, "bold"), fg="#ffffff", bg="#1a1a1a")
        title_label.pack(side="left", padx=15, pady=10)

        # Close button
        close_btn = tk.Label(self.title_bar, text="✕", font=("Segoe UI", 14, "bold"),
                            fg="#ffffff", bg="#1a1a1a", cursor="hand2", padx=15, pady=8)
        close_btn.pack(side="right")
        close_btn.bind("<Button-1>", lambda e: self.window.quit())
        close_btn.bind("<Enter>", lambda e: close_btn.config(bg="#ff4444"))
        close_btn.bind("<Leave>", lambda e: close_btn.config(bg="#1a1a1a"))

        # Minimize button
        min_btn = tk.Label(self.title_bar, text="−", font=("Segoe UI", 14, "bold"),
                          fg="#ffffff", bg="#1a1a1a", cursor="hand2", padx=15, pady=8)
        min_btn.pack(side="right")
        min_btn.bind("<Button-1>", lambda e: self.window.iconify())
        min_btn.bind("<Enter>", lambda e: min_btn.config(bg="#444444"))
        min_btn.bind("<Leave>", lambda e: min_btn.config(bg="#1a1a1a"))
    
    def create_main_content(self):
        # Main content frame
        self.main_frame = tk.Frame(self.window, bg="#0a0a0a")
        self.main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Create gradient background effect
        self.create_gradient_background()

        # Logo and title section
        self.create_header_section()

        # Modern button panel
        self.create_modern_button_panel()

        # Status bar at bottom
        self.create_status_bar()
    
    def create_gradient_background(self):
        # Create a subtle gradient effect using canvas
        self.bg_canvas = tk.Canvas(self.main_frame, width=860, height=540, highlightthickness=0)
        self.bg_canvas.place(x=0, y=0)

        # Create gradient from dark to slightly lighter
        for i in range(540):
            color_val = int(10 + (i / 540) * 15)  # Gradient from #0a0a0a to #191919
            color = f"#{color_val:02x}{color_val:02x}{color_val:02x}"
            self.bg_canvas.create_line(0, i, 860, i, fill=color, width=1)

    def create_header_section(self):
        # Header frame
        header_frame = tk.Frame(self.main_frame, bg="#0a0a0a", highlightthickness=0)
        header_frame.place(relx=0.5, rely=0.2, anchor="center")

        # Game title with modern styling
        title_label = tk.Label(header_frame, text="AION-BLITZ",
                              font=("Segoe UI", 36, "bold"),
                              fg="#00d4ff", bg="#0a0a0a", bd=0)
        title_label.pack()

        # Subtitle
        subtitle_label = tk.Label(header_frame, text="Premium Gaming Experience",
                                 font=("Segoe UI", 14),
                                 fg="#888888", bg="#0a0a0a", bd=0)
        subtitle_label.pack(pady=(5, 0))
    
    def create_modern_button_panel(self):
        # Button panel frame
        button_panel = tk.Frame(self.main_frame, bg="#0a0a0a", highlightthickness=0)
        button_panel.place(relx=0.5, rely=0.6, anchor="center")

        # Create modern buttons with better styling
        self.play_button = self.create_premium_button(button_panel, "▶ PLAY GAME", self.play_game, "#00d4ff", "#0099cc")
        self.play_button.pack(pady=10)

        self.website_button = self.create_premium_button(button_panel, "🌐 WEBSITE", self.open_website, "#444444", "#666666")
        self.website_button.pack(pady=5)

        self.check_updates_button = self.create_premium_button(button_panel, "⚡ CHECK UPDATES", self.check_updates, "#444444", "#666666")
        self.check_updates_button.pack(pady=5)

    def create_premium_button(self, parent, text, command, bg_color, hover_color):
        # Create button frame for better styling
        btn_frame = tk.Frame(parent, bg="#0a0a0a", highlightthickness=0)

        # Main button
        btn = tk.Label(btn_frame, text=text,
                      font=("Segoe UI", 14, "bold"),
                      fg="#ffffff", bg=bg_color,
                      cursor="hand2", padx=40, pady=15,
                      relief="flat", bd=0)
        btn.pack()

        # Button interactions
        btn.bind("<Button-1>", lambda e: command())
        btn.bind("<Enter>", lambda e: self.on_button_enter(btn, hover_color))
        btn.bind("<Leave>", lambda e: self.on_button_leave(btn, bg_color))

        return btn_frame

    def on_button_enter(self, button, hover_color):
        button.config(bg=hover_color)

    def on_button_leave(self, button, original_color):
        button.config(bg=original_color)

    def create_status_bar(self):
        # Status bar at bottom
        self.status_frame = tk.Frame(self.main_frame, bg="#0a0a0a", height=30)
        self.status_frame.pack(side="bottom", fill="x", pady=(20, 0))

        self.status_label = tk.Label(self.status_frame, text="Ready",
                                    font=("Segoe UI", 10),
                                    fg="#888888", bg="#0a0a0a", anchor="w")
        self.status_label.pack(side="left")

        # Version info
        version_label = tk.Label(self.status_frame, text="v1.0.0",
                                font=("Segoe UI", 10),
                                fg="#666666", bg="#0a0a0a", anchor="e")
        version_label.pack(side="right")

    def make_draggable(self):
        # Make window draggable
        def start_move(event):
            self.window.x = event.x
            self.window.y = event.y

        def stop_move(event):
            self.window.x = None
            self.window.y = None

        def do_move(event):
            if hasattr(self.window, 'x') and self.window.x is not None:
                deltax = event.x - self.window.x
                deltay = event.y - self.window.y
                x = self.window.winfo_x() + deltax
                y = self.window.winfo_y() + deltay
                self.window.geometry(f"+{x}+{y}")

        self.title_bar.bind("<Button-1>", start_move)
        self.title_bar.bind("<ButtonRelease-1>", stop_move)
        self.title_bar.bind("<B1-Motion>", do_move)

    def configure_styles(self):
        style = ttk.Style()
        style.configure("Modern.Horizontal.TProgressbar",
                       troughcolor="#1f2126",
                       background="#ff8800",
                       thickness=10)
    
    def play_game(self):
        self.window.withdraw()
        process_monitor_thread = threading.Thread(target=self.monitor_processes, daemon=True)
        process_monitor_thread.start()

        game_path = os.path.join(script_dir, "bin64", "AION.bin")
        game_command = f'start /affinity 7FFFFFFF "" "{game_path}" -ip:************ -port:2126 -cc:2 -lang:ENG --noauthgg -noweb -nobs -webshopevent:0 -st -ingamebrowser -charnamemenu -loginex -nowebshop -nokicks -ncg -noauthgg -ls -charnamemenu -ingameshop -DEVMODE "con_disable_console 0" -DEVMODE "g_chatlog 1" -DEVMODE "g_freefly 1" -DEVMODE "g_auto_disconnect 0" -win10-mouse-fix -fix-stack-win11'

        subprocess.run(game_command, shell=True)
    
    def open_website(self):
        webbrowser.open("https://aion-blitz.online")
    
    def monitor_processes(self):
        blacklisted_processes = [process.lower() for process in cheat_tools]
        while True:
            running_processes = [p.name().lower() for p in psutil.process_iter(['name'])]
            for process in running_processes:
                if process in blacklisted_processes:
                    messagebox.showwarning("Anti-Cheat Alert", 
                                         "Cheating software detected. The game will be terminated.")
                    for cheat_process in cheat_tools:
                        for p in psutil.process_iter(['name']):
                            if p.name().lower() == cheat_process.lower():
                                p.terminate()
                                break
                    self.terminate_game()
                    if self.window:
                        self.window.deiconify()
            time.sleep(1)
    
    def terminate_game(self):
        for process in psutil.process_iter(['name']):
            if process.name().lower() == "aion.bin":
                process.terminate()
                break
    
    def update_status(self, message):
        if hasattr(self, 'status_label'):
            self.status_label.config(text=message)

    def check_updates(self):
        # Disable button and update status
        self.check_updates_button.pack_forget()
        self.update_status("Checking for updates...")

        # Create progress frame
        progress_frame = tk.Frame(self.main_frame, bg="#0a0a0a")
        progress_frame.place(relx=0.5, rely=0.75, anchor="center")

        progress_bar = ttk.Progressbar(progress_frame, orient=tk.HORIZONTAL,
                                     length=400, mode='determinate',
                                     style="Modern.Horizontal.TProgressbar")
        progress_bar.pack(pady=10)

        progress_label = tk.Label(progress_frame, text="0%",
                                font=("Segoe UI", 12), bg="#0a0a0a", fg="#ffffff")
        progress_label.pack(pady=5)
        
        def download_progress(block_count, block_size, total_size):
            if total_size > 0:
                downloaded = block_count * block_size
                progress_percentage = int((downloaded / total_size) * 100)
                progress_bar['value'] = progress_percentage
                progress_label['text'] = f"{progress_percentage}%"
                progress_frame.update()
        
        def download_and_extract():
            try:
                patch_files = get_patch_files("https://aion-blitz.online/aion-blitz/Game-Launcher/Patches/")
                if not patch_files:
                    messagebox.showinfo("Update Check", "No updates available.")
                    progress_frame.destroy()
                    self.check_updates_button.pack(pady=5)
                    self.update_status("No updates available")
                    return
                
                current_version = get_current_version()
                filtered_patch_files = []
                
                if current_version is None:
                    for patch_file in sorted(patch_files):
                        try:
                            version_match = re.search(r'patch_(\d+\.\d+\.\d+)', patch_file)
                            if version_match:
                                filtered_patch_files.append(patch_file)
                                break
                        except Exception:
                            continue
                    
                    if not filtered_patch_files:
                        messagebox.showinfo("Update Check", "No valid patch files found.")
                        progress_frame.destroy()
                        self.check_updates_button.config(state=tk.NORMAL)
                        return
                else:
                    for patch_file in patch_files:
                        try:
                            version_match = re.search(r'patch_(\d+\.\d+\.\d+)', patch_file)
                            if version_match:
                                patch_version = version_match.group(1)
                                if version.parse(patch_version) > version.parse(current_version):
                                    filtered_patch_files.append(patch_file)
                        except (AttributeError, ValueError) as e:
                            print(f"Error parsing version from {patch_file}: {e}")
                            continue
                
                if not filtered_patch_files:
                    messagebox.showinfo("Update Check", "Your game is up to date!")
                    progress_frame.destroy()
                    self.check_updates_button.pack(pady=5)
                    self.update_status("Game is up to date")
                    return
                
                total_files = len(filtered_patch_files)
                for i, patch_file in enumerate(filtered_patch_files, start=1):
                    patch_url = "https://aion-blitz.online/aion-blitz/Game-Launcher/Patches/" + patch_file
                    patch_path = os.path.join(script_dir, patch_file)
                    req = urllib.request.Request(patch_url, headers={"User-Agent": "MyGameLauncher"})
                    with urllib.request.urlopen(req) as response, open(patch_path, 'wb') as out_file:
                        total_size = int(response.info().get('Content-Length', 0))
                        block_size = 8192
                        block_count = 0
                        while True:
                            buffer = response.read(block_size)
                            if not buffer:
                                break
                            out_file.write(buffer)
                            block_count += 1
                            download_progress(block_count, block_size, total_size)
                    
                    extract_patch(patch_path)
                    os.remove(patch_path)
                
                latest_patch = filtered_patch_files[-1]
                version_match = re.search(r'patch_(\d+\.\d+\.\d+)', latest_patch)
                if version_match:
                    latest_version = version_match.group(1)
                    save_version(latest_version)
                else:
                    raise ValueError(f"Could not parse version from patch file: {latest_patch}")
                
                progress_frame.destroy()
                messagebox.showinfo("Update Completed", "Updates have been applied successfully.")
                self.check_updates_button.pack(pady=5)
                self.update_status("Updates completed successfully")
            except Exception as e:
                messagebox.showerror("Update Error", f"An error occurred during the update process: {str(e)}")
                progress_frame.destroy()
                self.check_updates_button.pack(pady=5)
                self.update_status("Update failed")
        
        thread = threading.Thread(target=download_and_extract)
        thread.start()
    
    def run(self):
        self.window.mainloop()



# Download the background image with fallback
def download_background_image(url):
    filename = os.path.join(script_dir, "background_image.png")
    if not os.path.exists(filename):
        try:
            req = urllib.request.Request(url, headers={"User-Agent": "MyGameLauncher"})
            with urllib.request.urlopen(req) as response, open(filename, 'wb') as out_file:
                out_file.write(response.read())
        except Exception as e:
            print(f"Failed to download background image: {e}")

# Fetch cheat tool list
def fetch_cheat_tool_list():
    url = "https://aion-blitz.online/Game-Launcher/anti-cheat/cheating.txt"
    headers = {"User-Agent": "MyGameLauncher"}
    try:
        response = requests.get(url, headers=headers, timeout=5)
        response.raise_for_status()
        cheat_data = response.text.splitlines()
        if not cheat_data:
            print("Cheat list is empty or invalid.")
        return cheat_data
    except requests.RequestException as e:
        print(f"Failed to fetch the cheat tool list: {e}")
        print("Continuing without anti-cheat monitoring...")
        return []

# Fetch the list of patch files from the server with custom User-Agent
def get_patch_files(url):
    try:
        req = urllib.request.Request(url, headers={"User-Agent": "MyGameLauncher"})
        response = urllib.request.urlopen(req)
        html = response.read().decode()
        patch_files = re.findall(r'href=\"([^\"]+\.zip)\"', html)
        return patch_files
    except Exception as e:
        print(f"Error fetching patch files: {e}")
        return []

def get_current_version():
    version_file = os.path.join(script_dir, "version.txt")
    if os.path.exists(version_file):
        try:
            with open(version_file, 'r') as f:
                version_str = f.read().strip()
                if not version_str:  # If file is empty
                    return None
                return version_str
        except Exception as e:
            print(f"Error reading version file: {e}")
    return None

def save_version(version_str):
    version_file = os.path.join(script_dir, "version.txt")
    try:
        with open(version_file, 'w') as f:
            f.write(version_str)
    except Exception as e:
        print(f"Error saving version: {e}")

def extract_patch(patch_path):
    with zipfile.ZipFile(patch_path, 'r') as zip_ref:
        zip_ref.extractall(script_dir)

# Initialize cheat tools list
cheat_tools = fetch_cheat_tool_list()

# Create and run the launcher
if __name__ == "__main__":
    launcher = ModernLauncher()
    launcher.run()
