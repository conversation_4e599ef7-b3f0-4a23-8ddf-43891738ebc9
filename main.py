import os
import sys
import psutil
import subprocess
import threading
import time
import tkinter as tk
from tkinter import ttk
import webbrowser
from PIL import Image, ImageTk, ImageSequence
import urllib.request
import zipfile
from tkinter import messagebox
from tkinter.ttk import Progressbar
import re
import requests

from packaging import version
import math

# Determine base directory
if getattr(sys, 'frozen', False):
    script_dir = os.path.dirname(sys.executable)
else:
    script_dir = os.path.dirname(os.path.abspath(__file__))

class SplashScreen:
    def __init__(self):
        self.splash = tk.Toplevel()
        self.splash.overrideredirect(True)
        self.splash.attributes('-alpha', 0.0)
        
        # Get screen dimensions
        screen_width = self.splash.winfo_screenwidth()
        screen_height = self.splash.winfo_screenheight()
        
        # Set splash window size
        splash_width = 400
        splash_height = 300
        
        # Calculate position for center of screen
        x = (screen_width - splash_width) // 2
        y = (screen_height - splash_height) // 2
        
        self.splash.geometry(f'{splash_width}x{splash_height}+{x}+{y}')
        
        # Create main frame
        self.frame = tk.Frame(self.splash, bg='#1f2126')
        self.frame.pack(fill='both', expand=True)
        
        # Load and display logo
        try:
            logo_path = os.path.join(script_dir, "logo.png")
            if not os.path.exists(logo_path):
                # Download logo if not exists
                logo_url = "https://aion-blitz.online/Game-Launcher/Images/logo.png"
                urllib.request.urlretrieve(logo_url, logo_path)
            
            logo_img = Image.open(logo_path)
            logo_img = logo_img.resize((200, 200), Image.Resampling.LANCZOS)
            self.logo_photo = ImageTk.PhotoImage(logo_img)
            self.logo_label = tk.Label(self.frame, image=self.logo_photo, bg='#1f2126')
            self.logo_label.pack(pady=20)
        except Exception as e:
            print(f"Error loading logo: {e}")
        
        # Loading text
        self.loading_label = tk.Label(self.frame, text="Loading...", font=("Arial", 12), bg='#1f2126', fg='white')
        self.loading_label.pack(pady=10)
        
        # Progress bar
        self.progress = ttk.Progressbar(self.frame, length=300, mode='determinate', style="Splash.Horizontal.TProgressbar")
        self.progress.pack(pady=10)
        
        # Configure progress bar style
        style = ttk.Style()
        style.configure("Splash.Horizontal.TProgressbar", 
                       troughcolor='#1f2126',
                       background='#ff8800',
                       thickness=10)
        
        # Start fade in animation
        self.fade_in()
        
    def fade_in(self):
        alpha = self.splash.attributes('-alpha')
        if alpha < 1.0:
            alpha += 0.1
            self.splash.attributes('-alpha', alpha)
            self.splash.after(20, self.fade_in)
        else:
            self.animate_progress()
    
    def animate_progress(self):
        value = self.progress['value']
        if value < 100:
            value += 2
            self.progress['value'] = value
            self.splash.after(20, self.animate_progress)
        else:
            self.fade_out()
    
    def fade_out(self):
        alpha = self.splash.attributes('-alpha')
        if alpha > 0:
            alpha -= 0.1
            self.splash.attributes('-alpha', alpha)
            self.splash.after(20, self.fade_out)
        else:
            self.splash.destroy()

class ModernLauncher:
    def __init__(self):
        self.window = tk.Tk()
        self.window.title("Microsoft-AS")
        self.window.configure(bg="#1f2126")
        self.window.resizable(False, False)
        
        # Show splash screen
        self.splash = SplashScreen()
        
        # Configure window size and position
        window_width, window_height = 820, 700
        screen_width, screen_height = self.window.winfo_screenwidth(), self.window.winfo_screenheight()
        x, y = (screen_width // 2) - (window_width // 2), (screen_height // 2) - (window_height // 2)
        self.window.geometry(f"{window_width}x{window_height}+{x}+{y}")
        
        # Load background
        self.load_background()
        
        # Create logo/title
        self.create_logo()
        
        # Create modern button group
        self.create_button_group()
        
        # Configure styles
        self.configure_styles()
        
        # Start update check after splash screen
        self.window.after(500, self.check_updates)
        
    def load_background(self):
        background_image_path = os.path.join(script_dir, "background_image.png")
        if os.path.exists(background_image_path):
            try:
                background_image = Image.open(background_image_path)
                background_image = background_image.resize((820, 700), Image.Resampling.LANCZOS)
                self.background_photo = ImageTk.PhotoImage(background_image)
                self.background_label = tk.Label(self.window, image=self.background_photo, borderwidth=0)
                self.background_label.place(x=0, y=0, relwidth=1, relheight=1)
            except Exception as e:
                print(f"Could not load background image: {e}")
        else:
            self.background_label = tk.Label(self.window, text="Background Image Missing", 
                                           font=("Segoe UI", 16), fg="white", bg="#1f2126")
            self.background_label.place(x=0, y=0, relwidth=1, relheight=1)
    
    def create_logo(self):
        logo_path = os.path.join(script_dir, "logo.png")
        logo_frame = tk.Frame(self.window, bg='#1f2126', highlightthickness=0)
        logo_frame.place(relx=0.5, rely=0.23, anchor="center")
        logo_loaded = False
        if os.path.exists(logo_path):
            try:
                logo_img = Image.open(logo_path)
                logo_img = logo_img.resize((120, 120), Image.Resampling.LANCZOS)
                self.logo_photo = ImageTk.PhotoImage(logo_img)
                logo_label = tk.Label(logo_frame, image=self.logo_photo, bg='#1f2126', borderwidth=0)
                logo_label.pack()
                logo_loaded = True
            except Exception as e:
                print(f"Could not load logo: {e}")
        # Game title below logo (always show title)
        title_label = tk.Label(logo_frame, text="AION SOUL 8.4", font=("Segoe UI", 28, "bold"), fg="#fff", bg='#1f2126', bd=0, highlightthickness=0)
        title_label.pack(pady=(10, 0))
    
    def create_button_group(self):
        # Glassy, rounded, drop-shadowed frame
        self.button_frame = tk.Canvas(self.window, width=340, height=240, bg='', highlightthickness=0)
        self.button_frame.place(relx=0.5, rely=0.55, anchor="center")
        # Draw rounded rectangle for glass effect
        self.draw_rounded_rect(self.button_frame, 10, 10, 330, 230, 30, fill="#222a", outline="#fff2", shadow=True)

        # Place buttons on top of the canvas
        self.inner_frame = tk.Frame(self.button_frame, bg='', highlightthickness=0)
        self.button_frame.create_window(170, 120, window=self.inner_frame)

        # Modern glassy buttons
        self.play_button = self.create_modern_button(self.inner_frame, "Play", self.play_game)
        self.play_button.pack(pady=(20, 10), ipadx=30, ipady=10)
        self.website_button = self.create_modern_button(self.inner_frame, "Website", self.open_website)
        self.website_button.pack(pady=10, ipadx=30, ipady=10)
        self.check_updates_button = self.create_modern_button(self.inner_frame, "Check Updates", self.check_updates)
        self.check_updates_button.pack(pady=(10, 20), ipadx=30, ipady=10)
    
    def draw_rounded_rect(self, canvas, x1, y1, x2, y2, r, fill, outline, shadow=False):
        # Optional drop shadow
        if shadow:
            canvas.create_rectangle(x1+6, y1+6, x2+6, y2+6, outline='', fill='#0005')
        # Main rounded rectangle
        points = [x1+r, y1,
                  x2-r, y1,
                  x2, y1,
                  x2, y1+r,
                  x2, y2-r,
                  x2, y2,
                  x2-r, y2,
                  x1+r, y2,
                  x1, y2,
                  x1, y2-r,
                  x1, y1+r,
                  x1, y1]
        canvas.create_polygon(points, smooth=True, fill=fill, outline=outline, width=2)

    def create_modern_button(self, parent, text, command):
        btn = tk.Label(parent, text=text, font=("Segoe UI", 18, "bold"), fg="#fff", bg="#ff8800cc", cursor="hand2",
                       bd=0, relief="flat", padx=10, pady=5, highlightthickness=0)
        btn.bind("<Button-1>", lambda e: command())
        btn.bind("<Enter>", lambda e: btn.config(bg="#ffaa00ee", fg="#222"))
        btn.bind("<Leave>", lambda e: btn.config(bg="#ff8800cc", fg="#fff"))
        btn.config(borderwidth=0)
        return btn

    def configure_styles(self):
        style = ttk.Style()
        style.configure("Modern.Horizontal.TProgressbar",
                       troughcolor="#1f2126",
                       background="#ff8800",
                       thickness=10)
    
    def play_game(self):
        self.window.withdraw()
        process_monitor_thread = threading.Thread(target=self.monitor_processes, daemon=True)
        process_monitor_thread.start()

        game_path = os.path.join(script_dir, "bin64", "AION.bin")
        game_command = f'start /affinity 7FFFFFFF "" "{game_path}" -ip:************ -port:2126 -cc:2 -lang:ENG --noauthgg -noweb -nobs -webshopevent:0 -st -ingamebrowser -charnamemenu -loginex -nowebshop -nokicks -ncg -noauthgg -ls -charnamemenu -ingameshop -DEVMODE "con_disable_console 0" -DEVMODE "g_chatlog 1" -DEVMODE "g_freefly 1" -DEVMODE "g_auto_disconnect 0" -win10-mouse-fix -fix-stack-win11'

        subprocess.run(game_command, shell=True)
    
    def open_website(self):
        webbrowser.open("https://aion-blitz.online")
    
    def monitor_processes(self):
        blacklisted_processes = [process.lower() for process in cheat_tools]
        while True:
            running_processes = [p.name().lower() for p in psutil.process_iter(['name'])]
            for process in running_processes:
                if process in blacklisted_processes:
                    messagebox.showwarning("Anti-Cheat Alert", 
                                         "Cheating software detected. The game will be terminated.")
                    for cheat_process in cheat_tools:
                        for p in psutil.process_iter(['name']):
                            if p.name().lower() == cheat_process.lower():
                                p.terminate()
                                break
                    self.terminate_game()
                    if self.window:
                        self.window.deiconify()
            time.sleep(1)
    
    def terminate_game(self):
        for process in psutil.process_iter(['name']):
            if process.name().lower() == "aion.bin":
                process.terminate()
                break
    
    def check_updates(self):
        self.check_updates_button.config(state=tk.DISABLED)
        
        progress_frame = tk.Frame(self.window, bg="#1f2126")
        progress_frame.place(relx=0.5, rely=0.7, anchor="center")
        
        progress_bar = ttk.Progressbar(progress_frame, orient=tk.HORIZONTAL,
                                     length=400, mode='determinate',
                                     style="Modern.Horizontal.TProgressbar")
        progress_bar.pack(pady=10)
        
        progress_label = tk.Label(progress_frame, text="0%",
                                font=("Arial", 14), bg="#1f2126", fg="white")
        progress_label.pack(pady=5)
        
        def download_progress(block_count, block_size, total_size):
            if total_size > 0:
                downloaded = block_count * block_size
                progress_percentage = int((downloaded / total_size) * 100)
                progress_bar['value'] = progress_percentage
                progress_label['text'] = f"{progress_percentage}%"
                progress_frame.update()
        
        def download_and_extract():
            try:
                patch_files = get_patch_files("https://aion-blitz.online/Game-Launcher/Patches/")
                if not patch_files:
                    messagebox.showinfo("Update Check", "No updates available.")
                    progress_frame.destroy()
                    self.check_updates_button.config(state=tk.NORMAL)
                    return
                
                current_version = get_current_version()
                filtered_patch_files = []
                
                if current_version is None:
                    for patch_file in sorted(patch_files):
                        try:
                            version_match = re.search(r'patch_(\d+\.\d+\.\d+)', patch_file)
                            if version_match:
                                filtered_patch_files.append(patch_file)
                                break
                        except Exception:
                            continue
                    
                    if not filtered_patch_files:
                        messagebox.showinfo("Update Check", "No valid patch files found.")
                        progress_frame.destroy()
                        self.check_updates_button.config(state=tk.NORMAL)
                        return
                else:
                    for patch_file in patch_files:
                        try:
                            version_match = re.search(r'patch_(\d+\.\d+\.\d+)', patch_file)
                            if version_match:
                                patch_version = version_match.group(1)
                                if version.parse(patch_version) > version.parse(current_version):
                                    filtered_patch_files.append(patch_file)
                        except (AttributeError, ValueError) as e:
                            print(f"Error parsing version from {patch_file}: {e}")
                            continue
                
                if not filtered_patch_files:
                    messagebox.showinfo("Update Check", "Your game is up to date!")
                    progress_frame.destroy()
                    self.check_updates_button.config(state=tk.NORMAL)
                    return
                
                total_files = len(filtered_patch_files)
                for i, patch_file in enumerate(filtered_patch_files, start=1):
                    patch_url = "https://aion-blitz.online/Game-Launcher/Patches/" + patch_file
                    patch_path = os.path.join(script_dir, patch_file)
                    req = urllib.request.Request(patch_url, headers={"User-Agent": "MyGameLauncher"})
                    with urllib.request.urlopen(req) as response, open(patch_path, 'wb') as out_file:
                        total_size = int(response.info().get('Content-Length', 0))
                        block_size = 8192
                        block_count = 0
                        while True:
                            buffer = response.read(block_size)
                            if not buffer:
                                break
                            out_file.write(buffer)
                            block_count += 1
                            download_progress(block_count, block_size, total_size)
                    
                    extract_patch(patch_path)
                    os.remove(patch_path)
                
                latest_patch = filtered_patch_files[-1]
                version_match = re.search(r'patch_(\d+\.\d+\.\d+)', latest_patch)
                if version_match:
                    latest_version = version_match.group(1)
                    save_version(latest_version)
                else:
                    raise ValueError(f"Could not parse version from patch file: {latest_patch}")
                
                progress_frame.destroy()
                messagebox.showinfo("Update Completed", "Updates have been applied successfully.")
                self.check_updates_button.config(state=tk.NORMAL)
            except Exception as e:
                messagebox.showerror("Update Error", f"An error occurred during the update process: {str(e)}")
                progress_frame.destroy()
                self.check_updates_button.config(state=tk.NORMAL)
        
        thread = threading.Thread(target=download_and_extract)
        thread.start()
    
    def run(self):
        self.window.mainloop()



# Download the background image with fallback
def download_background_image(url):
    filename = os.path.join(script_dir, "background_image.png")
    if not os.path.exists(filename):
        try:
            req = urllib.request.Request(url, headers={"User-Agent": "MyGameLauncher"})
            with urllib.request.urlopen(req) as response, open(filename, 'wb') as out_file:
                out_file.write(response.read())
        except Exception as e:
            print(f"Failed to download background image: {e}")

# Fetch cheat tool list
def fetch_cheat_tool_list():
    url = "https://aionsoul.com/Game-Launcher/anti-cheat/cheating.txt"
    headers = {"User-Agent": "MyGameLauncher"}
    try:
        response = requests.get(url, headers=headers, timeout=5)
        response.raise_for_status()
        cheat_data = response.text.splitlines()
        if not cheat_data:
            print("Cheat list is empty or invalid.")
        return cheat_data
    except requests.RequestException as e:
        print(f"Failed to fetch the cheat tool list: {e}")
        return []

# Fetch the list of patch files from the server with custom User-Agent
def get_patch_files(url):
    try:
        req = urllib.request.Request(url, headers={"User-Agent": "MyGameLauncher"})
        response = urllib.request.urlopen(req)
        html = response.read().decode()
        patch_files = re.findall(r'href=\"([^\"]+\.zip)\"', html)
        return patch_files
    except Exception as e:
        print(f"Error fetching patch files: {e}")
        return []

def get_current_version():
    version_file = os.path.join(script_dir, "version.txt")
    if os.path.exists(version_file):
        try:
            with open(version_file, 'r') as f:
                version_str = f.read().strip()
                if not version_str:  # If file is empty
                    return None
                return version_str
        except Exception as e:
            print(f"Error reading version file: {e}")
    return None

def save_version(version_str):
    version_file = os.path.join(script_dir, "version.txt")
    try:
        with open(version_file, 'w') as f:
            f.write(version_str)
    except Exception as e:
        print(f"Error saving version: {e}")

def extract_patch(patch_path):
    with zipfile.ZipFile(patch_path, 'r') as zip_ref:
        zip_ref.extractall(script_dir)

# Initialize cheat tools list
cheat_tools = fetch_cheat_tool_list()

# Create and run the launcher
if __name__ == "__main__":
    launcher = ModernLauncher()
    launcher.run()
